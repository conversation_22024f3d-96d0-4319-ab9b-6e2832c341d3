# Default Parameters Consolidation

## Overview

This document describes the consolidation of default parameters for font size and margins into a single centralized location.

## Changes Made

### 1. Created Centralized Configuration File

**File:** `src/config/defaults.js`

- Defines `DEFAULT_OPTIONS` object with all default values:
  - `fontSize: "12px"`
  - `lineHeight: 1`
  - `marginTop: "20px"`
  - `marginBottom: "20px"`
  - `marginLeft: "25px"`
  - `marginRight: "25px"`

- Provides utility functions:
  - `getDefaultOptions(overrides)` - Merges defaults with custom values
  - `validateOptions(options)` - Validates and normalizes option values

### 2. Updated Files to Use Centralized Defaults

#### CLI Interface (`src/cli.js`)
- Imported `DEFAULT_OPTIONS` from centralized config
- Updated all yargs option defaults to reference `DEFAULT_OPTIONS` properties
- Ensures CLI help shows consistent default values

#### Generator Class (`src/generator.js`)
- Imported `DEFAULT_OPTIONS` and `validateOptions` from centralized config
- Updated `generateAdditionalStyles()` method to use `validateOptions()`
- Updated `htmlToPDF()` and `htmlToPDFBuffer()` methods to use `DEFAULT_OPTIONS` for fallback values

#### Main API Endpoint (`src/index.js`)
- Imported `validateOptions` from centralized config
- Updated options parsing to use `validateOptions()` function
- Ensures consistent default values across API endpoints

#### Vercel API Endpoint (`api/index.js`)
- Imported `validateOptions` from centralized config
- Updated options parsing to use `validateOptions()` function
- Eliminates inconsistent default values that were previously hardcoded

### 3. Updated Documentation

#### README.md
- Updated "Default Settings" section to reference the centralized configuration file
- Added note about centralization in `src/config/defaults.js`

## Benefits

1. **Single Source of Truth**: All default values are now defined in one location
2. **Consistency**: Eliminates discrepancies between different parts of the application
3. **Maintainability**: Changes to defaults only need to be made in one place
4. **Validation**: Centralized validation ensures proper data types and fallbacks
5. **Testability**: Easier to test and verify default behavior

## Previous Inconsistencies Resolved

Before consolidation, different parts of the application had different default values:

- **CLI & Generator**: `marginTop/Bottom: "20px"`, `marginLeft/Right: "25px"`
- **Main API**: `marginTop/Bottom: "70px"`, `marginLeft/Right: "45px"`
- **Vercel API**: `marginTop/Bottom: "40px"`, `marginLeft/Right: "40px"`

Now all components use the same defaults: `marginTop/Bottom: "20px"`, `marginLeft/Right: "25px"`

## Testing

A test script was created and run to verify:
- Default values are correctly defined
- Override functionality works properly
- Validation converts string values to appropriate types
- All tests passed successfully

## Usage

To modify default values in the future, simply update the `DEFAULT_OPTIONS` object in `src/config/defaults.js`. All components will automatically use the new defaults.

```javascript
// Example: To change default font size to 14px
export const DEFAULT_OPTIONS = {
  fontSize: "14px",  // Changed from "12px"
  lineHeight: 1,
  marginTop: "20px",
  marginBottom: "20px",
  marginLeft: "25px",
  marginRight: "25px",
};
```
