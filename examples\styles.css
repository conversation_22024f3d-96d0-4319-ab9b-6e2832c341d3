/* Backbone CSS for Resume Template 1 */

/* Basic */

#resume-preview [data-scope="vue-smart-pages"][data-part="page"] {
  background-color: white;
  color: black;
  text-align: justify;
  -moz-hyphens: auto;
  -ms-hyphens: auto;
  -webkit-hyphens: auto;
  hyphens: auto;
}

#resume-preview p,
#resume-preview li,
#resume-preview dl {
  margin: 0;
}

/* Headings */

#resume-preview h1,
#resume-preview h2,
#resume-preview h3 {
  font-weight: bold;
}

#resume-preview h1 {
  font-size: 2.13em;
}

#resume-preview h2,
#resume-preview h3 {
  margin-bottom: 3px;
  font-size: 1.2em;
}

#resume-preview h2 {
  border-bottom-style: solid;
  border-bottom-width: 1px;
}

/* Lists */

#resume-preview ul,
#resume-preview ol {
  padding-left: 1.5em;
  margin: 0.1em 0;
}

#resume-preview ul {
  list-style-type: circle;
}

#resume-preview ol {
  list-style-type: decimal;
}

/* Definition Lists */

#resume-preview dl {
  display: flex;
}

#resume-preview dl dt,
#resume-preview dl dd:not(:last-child) {
  flex: 1;
}

/* Tex */

#resume-preview :not(span.katex-display) > span.katex {
  font-size: 1em !important;
}

/* SVG & Images */

#resume-preview svg.iconify {
  vertical-align: -0.2em;
}

#resume-preview img {
  max-width: 100%;
}

/* Header */

#resume-preview .resume-header {
  text-align: center;
}

#resume-preview .resume-header h1 {
  text-align: center;
  line-height: 1;
  margin-bottom: 8px;
}

#resume-preview .resume-header-item:not(.no-separator)::after {
  content: " | ";
}

/* Citations */

#resume-preview [data-scope="cross-ref"][data-part="definitions"] {
  padding-left: 1.2em;
}

#resume-preview [data-scope="cross-ref"][data-part="definition"] p {
  margin-left: 0.5em;
}

#resume-preview [data-scope="cross-ref"][data-part="definition"]::marker {
  content: attr(data-label);
}

#resume-preview [data-scope="cross-ref"][data-part="reference"] {
  font-size: 100%;
  top: 0;
}

/* Dark & print mode */
/* You might want to comment out the following lines if you change the background or text color. */

.dark #resume-preview [data-scope="vue-smart-pages"][data-part="page"] {
  background-color: hsl(213, 12%, 15%);
  color: hsl(216, 12%, 84%);
}

@media print {
  .dark #resume-preview [data-scope="vue-smart-pages"][data-part="page"] {
    background-color: white;
    color: black;
  }
}

/* --- START: SUGGESTED EDITS FOR RESUME --- */

/* 1. GENERAL & SECTION HEADERS (H2)
   - Makes titles uppercase with a thicker underline.
   - Matches the style of 'PROFILE', 'PROJECTS', etc. in the screenshot.
*/
#resume-preview h2 {
  text-transform: uppercase;
  border-bottom-width: 2px; /* A bit thicker than the default 1px */
  padding-bottom: 2px; /* Adds a little space between text and line */
  margin-top: 6px; /* Reduced spacing above */
  margin-bottom: 6px; /* Reduced spacing below */
}

/* 2. HEADER CONTACT INFO
   - Removes the default "|" separator between contact items.
*/
#resume-preview .resume-header-item:not(.no-separator)::after {
  content: ""; /* Removes the pipe separator */
}

/* Optional: Add some spacing to mimic the screenshot's layout */
#resume-preview .resume-header-item {
  display: inline-block;
  margin: 0 4px;
  line-height: 1.5;
}

/* 3. EXPERIENCE & PROJECT ENTRY LAYOUT
   - This creates a flexbox layout for project/job titles with dates on the right
   - Dates should appear on the far right side
*/
#resume-preview .project-header {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  margin-top: 5px;
  margin-bottom: 3px;
}

#resume-preview .project-title {
  flex: 1;
}

#resume-preview .project-date {
  flex-shrink: 0;
  margin-left: 20px;
  font-style: italic;
  white-space: nowrap;
  color: #333333; /* Gray color for dates and locations */
}

/* Ensure proper spacing for subsection headers */
#resume-preview h3,
#resume-preview p > strong:first-child {
  margin-top: 6px;
  margin-bottom: 6px;
  display: block;
}

/* Keep project titles and taglines on same line */
#resume-preview .project-title p {
  margin: 0;
  line-height: 1.2;
  display: inline-block;
  width: 100%;
}

#resume-preview .project-title p strong,
#resume-preview .project-title p em,
#resume-preview .project-title p a {
  display: inline !important;
  white-space: normal;
}

#resume-preview .project-title p strong {
  margin-right: 2px;
}

#resume-preview .project-title p em {
  font-style: italic;
}

/* Reduce paragraph spacing throughout */
#resume-preview p {
  margin-bottom: 3px;
}

/* Reset the flex behavior of dt and dd from the original template */
#resume-preview dl dt,
#resume-preview dl dd {
  flex: none; /* Disables the 'flex: 1' rule that makes them equal width */
}

/* Ensure the date on the right doesn't wrap */
#resume-preview dl dd {
  white-space: nowrap;
}

/* 4. BULLET POINTS
   - Changes the list style from open circles to the solid discs seen in the screenshot.
*/
#resume-preview ul {
  list-style-type: disc; /* Was 'circle' */
  margin-left: 10px;
  margin-bottom: 4px;
}

#resume-preview .resume-header h1 + .resume-header-item {
  display: block; /* Overrides the default 'inline-block' to force a new line */
}

/* 5. LINKS STYLING
   - Makes links look like regular text (no blue color, no underline)
*/
#resume-preview a {
  color: inherit; /* Uses the same color as the parent element */
  text-decoration: none; /* Removes underline */
}

#resume-preview a:hover {
  color: inherit; /* Keeps the same color on hover */
  text-decoration: none; /* No underline on hover */
}

/* --- END: SUGGESTED EDITS FOR RESUME --- */
