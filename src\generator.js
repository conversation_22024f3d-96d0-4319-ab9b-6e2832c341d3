// Dynamic imports for serverless compatibility
let puppeteer;
let chromium;

// Check if we're running in production (Vercel, AWS Lambda, or other serverless)
const isProduction =
  process.env.VERCEL ||
  process.env.NODE_ENV === "production" ||
  process.env.AWS_LAMBDA_FUNCTION_NAME ||
  process.env.AWS_EXECUTION_ENV ||
  process.env.LAMBDA_TASK_ROOT;

console.log("Environment check:", {
  NODE_ENV: process.env.NODE_ENV,
  VERCEL: process.env.VERCEL,
  AWS_LAMBDA_FUNCTION_NAME: process.env.AWS_LAMBDA_FUNCTION_NAME,
  AWS_EXECUTION_ENV: process.env.AWS_EXECUTION_ENV,
  isProduction,
});

if (isProduction) {
  // Use serverless-compatible packages in production
  puppeteer = (await import("puppeteer-core")).default;
  chromium = (await import("@sparticuz/chromium")).default;

  console.log("Using serverless Chromium configuration");
} else {
  // Use full puppeteer in development
  puppeteer = (await import("puppeteer")).default;
  console.log("Using local Puppeteer configuration");
}

import MarkdownIt from "markdown-it";
import matter from "gray-matter";
import fs from "fs/promises";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class CVGenerator {
  constructor() {
    this.md = new MarkdownIt({
      html: true,
      linkify: true,
      typographer: true,
    });
  }

  async generatePDF(markdownPath, cssPath, outputPath, options = {}) {
    try {
      // Read markdown file
      const markdownContent = await fs.readFile(markdownPath, "utf-8");

      // Parse frontmatter and content
      const { data: frontmatter, content } = matter(markdownContent);

      // Generate HTML
      const html = await this.generateHTML(
        frontmatter,
        content,
        cssPath,
        options
      );

      // Convert to PDF
      await this.htmlToPDF(html, outputPath, options);

      return outputPath;
    } catch (error) {
      throw new Error(`Failed to generate PDF: ${error.message}`);
    }
  }

  async generatePDFBuffer(markdownContent, cssContent, options = {}) {
    try {
      // Parse frontmatter and content
      const { data: frontmatter, content } = matter(markdownContent);

      // Generate HTML
      const html = await this.generateHTMLFromContent(
        frontmatter,
        content,
        cssContent,
        options
      );

      // Convert to PDF buffer
      const pdfBuffer = await this.htmlToPDFBuffer(html, options);

      return pdfBuffer;
    } catch (error) {
      throw new Error(`Failed to generate PDF: ${error.message}`);
    }
  }

  async generateHTML(frontmatter, markdownContent, cssPath, options = {}) {
    // Parse markdown to HTML first
    const contentHTML = this.md.render(markdownContent);

    // Post-process HTML to handle ~ syntax
    const processedHTML = this.postProcessHTML(contentHTML);

    // Generate header HTML
    const headerHTML = this.generateHeader(frontmatter);

    // Read CSS - use default if cssPath is null/undefined
    let css;
    if (cssPath) {
      css = await fs.readFile(cssPath, "utf-8");
    } else {
      // Use default CSS from examples folder
      const defaultCssPath = path.join(
        __dirname,
        "..",
        "examples",
        "styles.css"
      );
      css = await fs.readFile(defaultCssPath, "utf-8");
    }

    // Read base template
    const templatePath = path.join(__dirname, "templates", "base.html");
    const template = await fs.readFile(templatePath, "utf-8");

    // Replace placeholders
    const html = template
      .replace("{{title}}", frontmatter.name || "CV")
      .replace("{{styles}}", css)
      .replace("{{additionalStyles}}", this.generateAdditionalStyles(options))
      .replace("{{header}}", headerHTML)
      .replace("{{content}}", processedHTML);

    return html;
  }

  async generateHTMLFromContent(
    frontmatter,
    markdownContent,
    cssContent,
    options = {}
  ) {
    // Parse markdown to HTML first
    const contentHTML = this.md.render(markdownContent);

    // Post-process HTML to handle ~ syntax
    const processedHTML = this.postProcessHTML(contentHTML);

    // Generate header HTML
    const headerHTML = this.generateHeader(frontmatter);

    // Use provided CSS content or default CSS
    let css;
    if (cssContent) {
      css = cssContent;
    } else {
      // Use default CSS from examples folder
      const defaultCssPath = path.join(
        __dirname,
        "..",
        "examples",
        "styles.css"
      );
      css = await fs.readFile(defaultCssPath, "utf-8");
    }

    // Read base template
    const templatePath = path.join(__dirname, "templates", "base.html");
    const template = await fs.readFile(templatePath, "utf-8");

    // Replace placeholders
    const html = template
      .replace("{{title}}", frontmatter.name || "CV")
      .replace("{{styles}}", css)
      .replace("{{additionalStyles}}", this.generateAdditionalStyles(options))
      .replace("{{header}}", headerHTML)
      .replace("{{content}}", processedHTML);

    return html;
  }

  postProcessHTML(html) {
    // Handle ~ syntax within paragraph tags
    // First regex: Match project entries with links (new format: entire title in link with mixed formatting)
    const projectRegex = /<p>(<a href="[^"]*">.*?<\/a>)\n~\s*([^]*?)<\/p>/g;

    // Legacy regex: Match project entries with links (old format: only project name in link)
    const projectLegacyRegex =
      /<p>(<strong><a href="[^"]*">.*?<\/a>,<\/strong> <em>.*?<\/em>)\n~\s*([^]*?)<\/p>/g;

    // Second regex: Match job entries with company links
    const jobWithLinkRegex =
      /<p>(<strong>[^<]*?,<\/strong> <em><a href="[^"]*">.*?<\/a><\/em>)\n~\s*([^]*?)<\/p>/g;

    // Third regex: Match job entries without links
    const jobRegex =
      /<p>(<strong>[^<]*?,<\/strong> <em>[^<]*?<\/em>)\n~\s*([^]*?)<\/p>/g;

    // Fourth regex: Match education entries (just strong tag, no em)
    const educationRegex = /<p>(<strong>[^<]*?,<\/strong>)\n~\s*([^]*?)<\/p>/g;

    let processedHTML = html.replace(
      projectRegex,
      (match, titlePart, datePart) => {
        const title = titlePart.trim();
        const date = datePart.trim();

        return `<div class="project-header">
<div class="project-title">
<p>${title}</p>
</div>
<div class="project-date">${date}</div>
</div>`;
      }
    );

    // Handle legacy project format for backward compatibility
    processedHTML = processedHTML.replace(
      projectLegacyRegex,
      (match, titlePart, datePart) => {
        const title = titlePart.trim();
        const date = datePart.trim();

        return `<div class="project-header">
<div class="project-title">
<p>${title}</p>
</div>
<div class="project-date">${date}</div>
</div>`;
      }
    );

    processedHTML = processedHTML.replace(
      jobWithLinkRegex,
      (match, titlePart, datePart) => {
        const title = titlePart.trim();
        const date = datePart.trim();

        return `<div class="project-header">
<div class="project-title">
<p>${title}</p>
</div>
<div class="project-date">${date}</div>
</div>`;
      }
    );

    processedHTML = processedHTML.replace(
      jobRegex,
      (match, titlePart, datePart) => {
        const title = titlePart.trim();
        const date = datePart.trim();

        return `<div class="project-header">
<div class="project-title">
<p>${title}</p>
</div>
<div class="project-date">${date}</div>
</div>`;
      }
    );

    processedHTML = processedHTML.replace(
      educationRegex,
      (match, titlePart, datePart) => {
        const title = titlePart.trim();
        const date = datePart.trim();

        return `<div class="project-header">
<div class="project-title">
<p>${title}</p>
</div>
<div class="project-date">${date}</div>
</div>`;
      }
    );

    return processedHTML;
  }

  generateHeader(frontmatter) {
    if (!frontmatter.name || !frontmatter.header) {
      return "";
    }

    let headerItems = frontmatter.header
      .map((item, index) => {
        const isLast = index === frontmatter.header.length - 1;
        const className = isLast
          ? "resume-header-item no-separator"
          : "resume-header-item";

        if (item.link) {
          return `<span class="${className}">
      <a href="${item.link}" target="_blank" rel="noopener noreferrer">${item.text}</a>
    </span>`;
        } else {
          return `<span class="${className}">
      ${item.text}
    </span>`;
        }
      })
      .join("\n");

    return `<div class="resume-header"><h1>${frontmatter.name}</h1>
${headerItems}</div>`;
  }

  generateAdditionalStyles(options) {
    const {
      fontSize = "12px",
      marginTop = "20px",
      marginBottom = "20px",
      marginLeft = "25px",
      marginRight = "25px",
      lineHeight = "1",
    } = options;

    // Always use Arial font fetched from Google Fonts
    const fontFamily = "'Arial', sans-serif";

    return `
      <style>
        #resume-preview [data-scope="vue-smart-pages"][data-part="page"] {
          font-family: ${fontFamily};
          font-size: ${fontSize};
          line-height: ${lineHeight};
          color: black;
        }

        #resume-preview p {
          margin-bottom: 5px;
        }

        @page {
          margin: ${marginTop} ${marginRight} ${marginBottom} ${marginLeft};
        }
      </style>
    `;
  }

  async htmlToPDF(html, outputPath, options = {}) {
    let browser;

    if (isProduction) {
      // Vercel/serverless configuration
      try {
        const executablePath = await chromium.executablePath();

        browser = await puppeteer.launch({
          args: [
            ...chromium.args,
            "--no-sandbox",
            "--disable-setuid-sandbox",
            "--disable-dev-shm-usage",
            "--disable-accelerated-2d-canvas",
            "--no-first-run",
            "--no-zygote",
            "--single-process",
            "--disable-gpu",
            "--disable-web-security",
            "--disable-features=VizDisplayCompositor",
          ],
          defaultViewport: chromium.defaultViewport,
          executablePath,
          headless: chromium.headless,
          ignoreHTTPSErrors: true,
        });
      } catch (error) {
        console.error("Failed to launch Chromium:", error);
        throw error;
      }
    } else {
      // Local development configuration - try system Chrome if puppeteer fails
      try {
        browser = await puppeteer.launch({
          headless: "new",
          args: ["--no-sandbox", "--disable-setuid-sandbox"],
        });
      } catch (error) {
        console.log("Puppeteer Chrome failed, trying system Chrome...");
        browser = await puppeteer.launch({
          executablePath:
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
          headless: "new",
          args: ["--no-sandbox", "--disable-setuid-sandbox"],
        });
      }
    }

    try {
      const page = await browser.newPage();

      // Set content
      await page.setContent(html, { waitUntil: "networkidle0" });

      // Wait for Google Fonts to load
      await page.evaluate(() => {
        return new Promise((resolve) => {
          // Check if fonts are loaded using FontFace API
          if (document.fonts && document.fonts.ready) {
            document.fonts.ready.then(() => {
              console.log("Fonts loaded successfully");
              resolve();
            });
          } else {
            // Fallback: wait for a reasonable time for fonts to load
            setTimeout(() => {
              console.log(
                "Font loading timeout - proceeding with PDF generation"
              );
              resolve();
            }, 2000);
          }
        });
      });

      // Wait for any iconify icons to load
      await page.evaluate(() => {
        return new Promise((resolve) => {
          if (typeof Iconify !== "undefined") {
            // Wait a bit for icons to render
            setTimeout(resolve, 1000);
          } else {
            resolve();
          }
        });
      });

      // Generate PDF with options
      const pdfOptions = {
        path: outputPath,
        format: "A4",
        printBackground: true,
        displayHeaderFooter: false,
        margin: {
          top: options.marginTop || "20px",
          bottom: options.marginBottom || "20px",
          left: options.marginLeft || "25px",
          right: options.marginRight || "25px",
        },
      };

      await page.pdf(pdfOptions);
    } finally {
      await browser.close();
    }
  }

  async htmlToPDFBuffer(html, options = {}) {
    let browser;

    if (isProduction) {
      // Vercel/serverless configuration
      try {
        console.log("Setting up Chromium for serverless environment...");

        // Configure Chromium for serverless
        const executablePath = await chromium.executablePath();
        console.log("Chromium executable path:", executablePath);

        browser = await puppeteer.launch({
          args: [
            ...chromium.args,
            "--no-sandbox",
            "--disable-setuid-sandbox",
            "--disable-dev-shm-usage",
            "--disable-accelerated-2d-canvas",
            "--no-first-run",
            "--no-zygote",
            "--single-process",
            "--disable-gpu",
            "--disable-web-security",
            "--disable-features=VizDisplayCompositor",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-ipc-flooding-protection",
            "--disable-extensions",
            "--disable-default-apps",
            "--disable-translate",
            "--disable-sync",
            "--disable-component-extensions-with-background-pages",
            "--disable-background-networking",
            "--disable-domain-reliability",
            "--disable-client-side-phishing-detection",
            "--disable-hang-monitor",
            "--disable-popup-blocking",
            "--disable-prompt-on-repost",
            "--disable-backgrounding-occluded-windows",
            "--disable-ipc-flooding-protection",
            "--memory-pressure-off",
            "--max_old_space_size=4096",
          ],
          defaultViewport: chromium.defaultViewport,
          executablePath,
          headless: chromium.headless,
          ignoreHTTPSErrors: true,
        });

        console.log("Chromium launched successfully in serverless mode");
      } catch (error) {
        console.error("Failed to launch Chromium (buffer):", error);
        console.error("Error details:", {
          message: error.message,
          stack: error.stack,
        });

        // Try fallback configuration
        console.log("Trying fallback Chromium configuration...");
        try {
          browser = await puppeteer.launch({
            args: [
              "--no-sandbox",
              "--disable-setuid-sandbox",
              "--disable-dev-shm-usage",
              "--disable-gpu",
              "--disable-web-security",
              "--single-process",
              "--no-zygote",
            ],
            headless: true,
            ignoreHTTPSErrors: true,
          });
          console.log("Fallback Chromium launched successfully");
        } catch (fallbackError) {
          console.error("Fallback Chromium also failed:", fallbackError);
          throw new Error(
            `Failed to launch Chromium in serverless environment: ${error.message}`
          );
        }
      }
    } else {
      // Local development configuration - try system Chrome if puppeteer fails
      try {
        browser = await puppeteer.launch({
          headless: "new",
          args: ["--no-sandbox", "--disable-setuid-sandbox"],
        });
      } catch (error) {
        console.log("Puppeteer Chrome failed, trying system Chrome...");
        browser = await puppeteer.launch({
          executablePath:
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
          headless: "new",
          args: ["--no-sandbox", "--disable-setuid-sandbox"],
        });
      }
    }

    try {
      const page = await browser.newPage();

      // Set content
      await page.setContent(html, { waitUntil: "networkidle0" });

      // Wait for Google Fonts to load
      await page.evaluate(() => {
        return new Promise((resolve) => {
          // Check if fonts are loaded using FontFace API
          if (document.fonts && document.fonts.ready) {
            document.fonts.ready.then(() => {
              console.log("Fonts loaded successfully");
              resolve();
            });
          } else {
            // Fallback: wait for a reasonable time for fonts to load
            setTimeout(() => {
              console.log(
                "Font loading timeout - proceeding with PDF generation"
              );
              resolve();
            }, 2000);
          }
        });
      });

      // Wait for any iconify icons to load
      await page.evaluate(() => {
        return new Promise((resolve) => {
          if (typeof Iconify !== "undefined") {
            // Wait a bit for icons to render
            setTimeout(resolve, 1000);
          } else {
            resolve();
          }
        });
      });

      // Generate PDF buffer with options
      const pdfOptions = {
        format: "A4",
        printBackground: true,
        displayHeaderFooter: false,
        margin: {
          top: options.marginTop || "20px",
          bottom: options.marginBottom || "20px",
          left: options.marginLeft || "25px",
          right: options.marginRight || "25px",
        },
      };

      const pdfBuffer = await page.pdf(pdfOptions);
      console.log("PDF generated successfully, buffer size:", pdfBuffer.length);
      return pdfBuffer;
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }
}

export default CVGenerator;
