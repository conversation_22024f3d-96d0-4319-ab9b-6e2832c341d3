import express from "express";
import multer from "multer";
import CVGenerator from "../src/generator.js";
import { authenticateApi<PERSON>ey } from "../src/middleware/auth.js";
import { validateOptions } from "../src/config/defaults.js";

const app = express();

// Use memory storage for serverless compatibility
const upload = multer({ storage: multer.memoryStorage() });
const generator = new CVGenerator();

// Enable CORS
app.use((req, res, next) => {
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  res.header(
    "Access-Control-Allow-Headers",
    "Origin, X-Requested-With, Content-Type, Accept, Authorization, X-API-Key, API-Key"
  );
  if (req.method === "OPTIONS") {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Root route - API info
app.get("/", (req, res) => {
  res.json({
    message: "CV Generator API",
    version: "1.0.0",
    endpoints: {
      "GET /api/health": "Health check (requires API key)",
      "POST /api/generate":
        "Generate CV from markdown and CSS files (requires API key)",
    },
    authentication:
      "Required for all endpoints. Use X-API-Key, Authorization (Bearer), or API-Key header",
    example:
      "curl -H 'X-API-Key: your-secret-key' https://cv-factory-kappa.vercel.app/api/health",
  });
});

// Health check endpoint
app.get("/health", authenticateApiKey, (req, res) => {
  res.json({ status: "ok", message: "CV Generator API is running" });
});

// Generate CV endpoint
app.post(
  "/generate",
  authenticateApiKey,
  upload.fields([
    { name: "markdown", maxCount: 1 },
    { name: "css", maxCount: 1 },
  ]),
  async (req, res) => {
    try {
      console.log("Generate endpoint called");
      const markdownFile = req.files["markdown"]?.[0];
      const cssFile = req.files["css"]?.[0];

      if (!markdownFile) {
        return res.status(400).json({
          error: "Markdown file is required",
        });
      }

      // Parse options from request body
      const options = validateOptions({
        fontSize: req.body.fontSize,
        lineHeight: req.body.lineHeight
          ? parseFloat(req.body.lineHeight)
          : undefined,
        marginTop: req.body.marginTop,
        marginBottom: req.body.marginBottom,
        marginLeft: req.body.marginLeft,
        marginRight: req.body.marginRight,
      });

      // Convert buffer to string for markdown content
      const markdownContent = markdownFile.buffer.toString("utf-8");
      const cssContent = cssFile ? cssFile.buffer.toString("utf-8") : null;

      console.log("Generating PDF with options:", options);

      // Generate PDF buffer directly (no file system writes)
      const pdfBuffer = await generator.generatePDFBuffer(
        markdownContent,
        cssContent,
        options
      );

      console.log("PDF generated, buffer length:", pdfBuffer.length);

      // Validate PDF buffer
      if (!pdfBuffer || pdfBuffer.length === 0) {
        throw new Error("Generated PDF buffer is empty");
      }

      // Set headers for PDF download
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader("Content-Disposition", 'attachment; filename="cv.pdf"');
      res.setHeader("Content-Length", pdfBuffer.length);

      // Use res.end() instead of res.send() for binary data in serverless
      res.end(pdfBuffer);
    } catch (error) {
      console.error("Error generating CV:", error);
      res.status(500).json({ error: error.message });
    }
  }
);

// Export the Express app
export default app;
