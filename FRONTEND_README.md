# CV Factory Frontend

A simple HTML frontend for the CV Factory project that allows users to generate professional CVs from Markdown content.

## Features

- **Markdown Input**: Paste your CV content in Markdown format
- **Default Styling**: Uses the beautiful default CSS from the examples folder automatically
- **Custom CSS Support**: Optional - upload your own CSS file for custom styling
- **Configurable Options**: Adjust font size, margins, and line height
- **Live Preview**: See status and download generated PDFs
- **Example Content**: Load example markdown to get started quickly

## Setup

1. **Start the Backend Server**:

   ```bash
   npm start
   # Server will run on http://localhost:3000
   ```

2. **Access the Frontend**:
   Open your browser and go to: `http://localhost:3000`

3. **API Key Setup**:
   - The backend requires an API key for security
   - For development, the default API key "dev-key" is pre-filled
   - For production, set the `API_KEY` environment variable when starting the server
   - Enter the correct key in the "API Key" field on the frontend

## Usage

1. **Enter API Key**: Input your API key in the designated field
2. **Add Markdown Content**:
   - Paste your CV markdown in the text area
   - Click "Load example markdown" to see the format
3. **Optional CSS**: Upload a custom CSS file if you want different styling
4. **Adjust Options**: Modify font size, margins, and line height as needed
5. **Generate CV**: Click "Generate CV PDF" to create and download your CV

## Markdown Format

The CV should be in Markdown format with YAML frontmatter for header information:

```markdown
---
name: Your Name
header:
  - text: <span class="iconify" data-icon="tabler:mail"></span> <EMAIL>
    link: mailto:<EMAIL>
  - text: <span class="iconify" data-icon="tabler:phone"></span> +1234567890
  - text: <span class="iconify" data-icon="tabler:map-pin"></span> Your City, Country
---

## Profile

Your professional summary here...

## Experience

**Job Title,** _Company Name_
~ Start Date – End Date

- Achievement or responsibility
- Another achievement

## Skills

**Technical Skills** — List your technical skills here

**Soft Skills** — List your soft skills here
```

## Default Styling

When no custom CSS is provided, the system automatically uses the default styling from `examples/styles.css`, which includes:

- Professional typography and spacing
- Clean section headers with underlines
- Proper project/job entry formatting with dates
- Icon support via Iconify
- Print-optimized layout
- Responsive design elements

## API Integration

The frontend communicates with the backend API at `/generate` endpoint:

- **Method**: POST
- **Headers**: `X-API-Key: your-api-key`
- **Body**: FormData with markdown file and optional CSS file
- **Response**: PDF file download

## Customization

You can customize the frontend by:

1. **Modifying Styles**: Edit the CSS in `public/index.html`
2. **Adding Features**: Extend the JavaScript functionality
3. **Changing Layout**: Modify the HTML structure
4. **API Configuration**: Update the `API_BASE` constant for different environments

## Troubleshooting

- **API Key Error**: Make sure you've entered the correct API key
- **Generation Failed**: Check that your markdown format is correct
- **Download Issues**: Ensure your browser allows file downloads
- **Server Connection**: Verify the backend is running on port 3000

## File Structure

```text
public/
└── index.html          # Complete frontend application
examples/
├── cv.md              # Example markdown format
└── styles.css         # Default CSS styling
```

The frontend is a single HTML file with embedded CSS and JavaScript for simplicity and easy deployment.
