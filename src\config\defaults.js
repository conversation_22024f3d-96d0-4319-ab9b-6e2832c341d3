/**
 * Default configuration values for CV generation
 * Centralized location for all default font size and margin settings
 */

export const DEFAULT_OPTIONS = {
  fontSize: "12px",
  lineHeight: 1,
  marginTop: "20px",
  marginBottom: "20px",
  marginLeft: "25px",
  marginRight: "25px",
};

/**
 * Get default options with optional overrides
 * @param {Object} overrides - Optional overrides for default values
 * @returns {Object} Merged options with defaults
 */
export function getDefaultOptions(overrides = {}) {
  return {
    ...DEFAULT_OPTIONS,
    ...overrides,
  };
}

/**
 * Validate and normalize option values
 * @param {Object} options - Options to validate
 * @returns {Object} Validated and normalized options
 */
export function validateOptions(options = {}) {
  const validated = getDefaultOptions(options);
  
  // Ensure lineHeight is a number
  if (typeof validated.lineHeight === 'string') {
    validated.lineHeight = parseFloat(validated.lineHeight) || DEFAULT_OPTIONS.lineHeight;
  }
  
  return validated;
}
