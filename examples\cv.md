---
name: <PERSON>
header:
  - text: |
      <span style="font-style: italic; font-weight: normal; display: block; margin-top: -10px;">
      Fresh Software Engineer
      </span>
  - text: <span class="iconify" data-icon="tabler:mail"></span> <EMAIL>
    link: mailto:<EMAIL>
  - text: <span class="iconify" data-icon="tabler:phone"></span> +201557528856
  - text: <span class="iconify" data-icon="tabler:map-pin"></span> Cairo, Egypt
  - text: <span class="iconify" data-icon="tabler:world"></span> creative-geek.tech
    link: https://creative-geek.tech
  - text: <span class="iconify" data-icon="tabler:brand-github"></span> github.com/Creative-Geek
    link: https://github.com/Creative-Geek
  - text: <span class="iconify" data-icon="tabler:brand-linkedin"></span> linkedin.com/in/ahmed-taha-thecg
    link: https://linkedin.com/in/ahmed-taha-thecg
  - text: <span class="iconify" data-icon="tabler:shield-check"></span> Exempted
---

## Profile

Freshly graduated Software Engineer with hands-on experience in web development, AI integrations & Automation, and multimedia production. Skilled in React, Nodejs, Flask, and Python, aspires to create dynamic, user-friendly applications. Has delivered projects from web solutions to AI-driven tools—including an Arabic Handwriting E2E OCR system. Strong in UI/UX design and committed to crafting efficient, engaging digital experiences.

## Projects

[**Tasky,** _AI-Powered Todo List_](https://tasky.creative-geek.tech/)
~ 04/2025 – 05/2025

- Developed a fullstack todo list app with React, Node.js, and Prisma, focusing on user-friendly design and smooth animations.
- Deployed the client, server, and Postgres database, while enforcing security best practices.
- Integrated an AI that turns pasted coworker messages into tasks automatically.

[**CG Blog,** _FOSS markdown dynamic site generator_](https://creative-geek.tech/)
~ 01/2025 – 06/2025

- Built a Head-only **React-based** blog and portfolio with **Shadcn** for use with any markdown files.
- Implemented custom dynamic **RTL** language support for Arabic, and SSR for SEO optimizations.
- Prioritized beginner-friendly UX with a minimalist design and **plug-and-play** configuration.

[**Digital-Ḍād ض-الرقمية,** _Graduation Project_](https://digital-dahd.vercel.app/)
~ 03/2023 – 07/2024

- Designed and implemented a **React** application for **scanning, recognizing,** and **grading** handwritten Arabic exams.
- Developed an OCR model with CNN and Bi-LSTM networks, achieving a 97% accuracy rate—an improvement from the previous 87%—using a **custom dataset** of more than 100,000 samples.
- Built an image labeling app with user contribution tracking using **React** and **Supabase**.
- Developed an **Agentic LLM** to grade exams based on a model answer using Vertex AI Platform.
- The research was published at "AI for SDGs" Conference in 2024, as well as in "IJT" journal.

[**The Platformer Test,** _Video Game Project_](https://creative-geek.github.io/Platformer-Test/)
~ 12/2022 – 12/2022

- Developed a fully functional platformer video game with 3 levels using **Godot Engine** without prior knowledge in a single week.

[**Time Estimator,** _Simple application to plan data transfers._](https://github.com/Creative-Geek/Estimator-App)
~ 05/2021 – 06/2021

- Created an app that **estimates data transfer times** based on speed, time, or size.
- Featuring a user friendly, intuitive, and OS-independent custom GUI using **QT5**.

## Skills

**Technical Skills** — **Programming & Frameworks:** Python, C++, JavaScript, Typescript, React, React Native, Next.js, Vue, Flask, FastAPI, Django, Node.js, WordPress. | **Generative AI:** LLMs, Agent AIs, LangChain, Stable Diffusion, Flux, Vertex AI Platform | **AI/ML:** TensorFlow, Image Processing. | **Cloud & Containerization:** Google Cloud, Azure, Docker. | **Tools & Technologies:** GitHub, Git, Jira, Linux, Prisma, SQLite, PostgreSQL, MongoDB, Godot Engine, QT. | **Multimedia & Design:** Graphic Design, Video Editing, Motion Graphics, Adobe Creative Suite, UI/UX Design. | **Technical Communication:** Technical Writing, Content Creation.

**Soft Skills** — Communication, Teamwork, Problem-Solving, Adaptability, Creativity, Time Management, Detail-Oriented.

## Technical Experience

[**Software Developer & IT Specialist,** _ELHODA MEP Contracting_](https://drive.google.com/file/d/1rYfz_IXZlfwyKy-3QbmTmnfzVAV4Si0H/view?usp=drive_link)
~ 09/2021 – 09/2022 | _Cairo, Egypt_

- Implemented and managed an on-premises **custom file server**.
- Developed **automation scripts** that interact with Excel and Autocad to enhance team efficiency.
- Provided software and hardware technical support.

[**Store Developer,** _CompuMall_](https://web.archive.org/web/20230921091114/compumal.com)
~ 06/2023 – 09/2023 | _Ismailia, Egypt_

- Developed the company E-Commerce website using **WordPress**.
- Performed on-site computer repairs, and resolved complex technical issues for customers.

**Graphics Designer,** _Kunai Store_
~ 07/2024 – 05/2025 | _Saudi Arabia (Remote)_

- Created visually engaging **designs** for websites, advertisements, and social media platforms, contributing to over 600% sales.

[**Tech Content Writer,** _E-CAMP_](https://www.facebook.com/e.camp.2020)
~ 07/2024 – 11/2024 | _Cairo, Egypt_

- Wrote articles simplifying technological concepts related to **AI** and **software engineering**.

## Education

**Faculty of Engineering, Suez Canal University,**
~ 09/2019 – 07/2024 | _Ismailia, Egypt_

_Bachelor in Computer Engineering | GPA: B- | Graduation Project Grade: Excellent_
