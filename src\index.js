import express from "express";
import multer from "multer";
import CV<PERSON>enerator from "./generator.js";
import { authenticate<PERSON><PERSON><PERSON><PERSON> } from "./middleware/auth.js";

const app = express();
// Use memory storage for serverless compatibility
const upload = multer({ storage: multer.memoryStorage() });
const generator = new CVGenerator();

app.use(express.json());

// Enable CORS for frontend
app.use((req, res, next) => {
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  res.header(
    "Access-Control-Allow-Headers",
    "Origin, X-Requested-With, Content-Type, Accept, Authorization, X-API-Key, API-Key"
  );
  if (req.method === "OPTIONS") {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Serve static files from public directory
app.use(express.static("public"));

// Health check - protected with API key
app.get("/health", authenticate<PERSON>pi<PERSON>ey, (req, res) => {
  res.json({ status: "ok", message: "CV Generator API is running" });
});

// Generate CV endpoint - protected with API key
app.post(
  "/generate",
  authenticateApiKey,
  upload.fields([
    { name: "markdown", maxCount: 1 },
    { name: "css", maxCount: 1 },
  ]),
  async (req, res) => {
    try {
      const markdownFile = req.files["markdown"]?.[0];
      const cssFile = req.files["css"]?.[0];

      if (!markdownFile) {
        return res.status(400).json({
          error: "Markdown file is required",
        });
      }

      // Parse options from request body
      const options = {
        fontSize: req.body.fontSize || "12px",
        lineHeight: parseFloat(req.body.lineHeight) || 1.3,
        marginTop: req.body.marginTop || "70px",
        marginBottom: req.body.marginBottom || "70px",
        marginLeft: req.body.marginLeft || "45px",
        marginRight: req.body.marginRight || "45px",
      };

      // Convert buffer to string for markdown content
      const markdownContent = markdownFile.buffer.toString("utf-8");
      const cssContent = cssFile ? cssFile.buffer.toString("utf-8") : null;

      // Generate PDF buffer directly (no file system writes)
      const pdfBuffer = await generator.generatePDFBuffer(
        markdownContent,
        cssContent,
        options
      );

      // Set headers for PDF download
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader("Content-Disposition", 'attachment; filename="cv.pdf"');
      res.setHeader("Content-Length", pdfBuffer.length);

      // Send PDF buffer directly
      res.send(pdfBuffer);
    } catch (error) {
      console.error("Error generating CV:", error);
      res.status(500).json({ error: error.message });
    }
  }
);

// Add a route to show API usage when accessed without authentication
app.get("/", (req, res) => {
  res.json({
    message: "CV Generator API",
    version: "1.0.0",
    endpoints: {
      "GET /health": "Health check (requires API key)",
      "POST /generate":
        "Generate CV from markdown file and optional CSS file (requires API key). If no CSS provided, uses default styling.",
    },
    authentication:
      "Required for all endpoints. Use X-API-Key, Authorization (Bearer), or API-Key header",
    example:
      "curl -H 'X-API-Key: your-secret-key' http://localhost:3000/health",
  });
});

// Handle 404 for unknown routes
app.use("*", (req, res) => {
  res.status(404).json({
    error: "Not Found",
    message: "The requested endpoint does not exist",
    availableEndpoints: ["/", "/health", "/generate"],
  });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`CV Generator server running on port ${PORT}`);
  console.log(
    `API Key authentication ${
      process.env.API_KEY
        ? "enabled"
        : "DISABLED - Please set API_KEY environment variable"
    }`
  );
});
