# CV Generator

A powerful Node.js application that generates professional PDF CVs from Markdown files with custom CSS styling. Features both CLI and REST API interfaces with special syntax for date positioning and responsive layouts.

## ✨ Features

- **Markdown to PDF**: Convert structured markdown CVs to professional PDFs
- **Custom Styling**: Full CSS customization support
- **Special Syntax**: `~` syntax for automatic date/location positioning
- **Responsive Layout**: Compact, single-page optimized layouts
- **Dual Interface**: Both CLI and REST API access
- **Secure API**: API key authentication for production use
- **Serverless Ready**: Vercel deployment compatible

## 🚀 Quick Start

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd CV-Factory-nodejs

# Install dependencies
npm install
# or
pnpm install

# Install Chrome for PDF generation (local development)
npx puppeteer browsers install chrome
# or
pnpm dlx puppeteer browsers install chrome
```

### CLI Usage

```bash
# Basic usage
node src/cli.js -i examples/cv.md -c examples/styles.css -o my-cv.pdf

# With custom options
node src/cli.js \
  -i examples/cv.md \
  -c examples/styles.css \
  -o compact-cv.pdf \
  --fontSize 11px \
  --lineHeight 1 \
  --marginTop 15px \
  --marginBottom 15px \
  --marginLeft 20px \
  --marginRight 20px
```

### API Usage

```bash
# Start the server
npm start
# or
node src/index.js

# Set API key (required for production)
export API_KEY="your-secret-api-key"
```

## 📖 Documentation

### CLI Interface

#### Command Options

| Option           | Alias | Description                    | Default  |
| ---------------- | ----- | ------------------------------ | -------- |
| `--input`        | `-i`  | Input markdown file (required) | -        |
| `--css`          | `-c`  | CSS file path (required)       | -        |
| `--output`       | `-o`  | Output PDF file                | `cv.pdf` |
| `--fontSize`     | -     | Font size                      | `12px`   |
| `--lineHeight`   | -     | Line height                    | `1`      |
| `--marginTop`    | -     | Top margin                     | `20px`   |
| `--marginBottom` | -     | Bottom margin                  | `20px`   |
| `--marginLeft`   | -     | Left margin                    | `25px`   |
| `--marginRight`  | -     | Right margin                   | `25px`   |

#### Examples

```bash
# Standard generation
node src/cli.js -i cv.md -c styles.css -o resume.pdf

# Ultra-compact single page
node src/cli.js -i cv.md -c styles.css -o compact.pdf \
  --fontSize 10px --marginTop 10px --marginBottom 10px

# Custom styling
node src/cli.js -i cv.md -c custom.css -o styled.pdf \
  --lineHeight 1.2 --fontSize 13px
```

### REST API

#### Authentication

All API endpoints require authentication using one of these headers:

```bash
# X-API-Key header
curl -H "X-API-Key: your-secret-key" http://localhost:3000/health

# Authorization Bearer header
curl -H "Authorization: Bearer your-secret-key" http://localhost:3000/health

# API-Key header
curl -H "API-Key: your-secret-key" http://localhost:3000/health
```

#### Endpoints

##### GET `/`

Get API information and available endpoints.

```bash
curl http://localhost:3000/
```

##### GET `/health`

Health check endpoint (requires authentication).

```bash
curl -H "X-API-Key: your-secret-key" http://localhost:3000/health
```

**Response:**

```json
{
  "status": "ok",
  "message": "CV Generator API is running"
}
```

##### POST `/generate`

Generate PDF from uploaded markdown and CSS files (requires authentication).

**Parameters:**

- `markdown` (file): Markdown CV file
- `css` (file): CSS styling file
- `fontSize` (string, optional): Font size (default: "12px")
- `lineHeight` (number, optional): Line height (default: 1)
- `marginTop` (string, optional): Top margin (default: "20px")
- `marginBottom` (string, optional): Bottom margin (default: "20px")
- `marginLeft` (string, optional): Left margin (default: "25px")
- `marginRight` (string, optional): Right margin (default: "25px")

**Example:**

```bash
curl -X POST \
  -H "X-API-Key: your-secret-key" \
  -F "markdown=@cv.md" \
  -F "css=@styles.css" \
  -F "fontSize=11px" \
  -F "lineHeight=1" \
  -F "marginTop=15px" \
  http://localhost:3000/generate \
  --output generated-cv.pdf
```

**JavaScript Example:**

```javascript
const formData = new FormData();
formData.append("markdown", markdownFile);
formData.append("css", cssFile);
formData.append("fontSize", "11px");
formData.append("lineHeight", "1");

const response = await fetch("http://localhost:3000/generate", {
  method: "POST",
  headers: {
    "X-API-Key": "your-secret-key",
  },
  body: formData,
});

if (response.ok) {
  const blob = await response.blob();
  // Handle the PDF blob
}
```

## 📝 Markdown Format

### Frontmatter

Your markdown file should start with YAML frontmatter:

```yaml
---
name: John Doe
header:
  - text: <span class="iconify" data-icon="tabler:mail"></span> <EMAIL>
    link: mailto:<EMAIL>
  - text: <span class="iconify" data-icon="tabler:phone"></span> +1234567890
  - text: <span class="iconify" data-icon="tabler:map-pin"></span> New York, NY
  - text: <span class="iconify" data-icon="tabler:world"></span> johndoe.com
    link: https://johndoe.com
---
```

### Special `~` Syntax

Use `~` at the beginning of a line to automatically position dates/locations on the right:

```markdown
## Projects

**[Project Name](https://link.com),** _Project Description_
~ 01/2023 – 12/2023

- Project bullet points
- More details

## Experience

**Job Title,** _[Company Name](https://company.com)_
~ 06/2021 – 12/2022 | New York, NY

- Job responsibilities
- Achievements

**Another Job,** _Company Without Link_
~ 01/2020 – 05/2021 | Remote

## Education

**Degree Name, University Name,**
~ 09/2016 – 05/2020 | City, State
```

### Output Format

The `~` syntax automatically generates this layout:

```
Project Name, Project Description        01/2023 – 12/2023
Job Title, Company Name           06/2021 – 12/2022 | New York, NY
```

## 🎨 CSS Styling

The generator uses a responsive CSS framework. Key classes:

- `.project-header`: Container for title and date
- `.project-title`: Left-aligned content (title, company)
- `.project-date`: Right-aligned content (dates, location)
- `.resume-header`: Header section with name and contact info

### Example CSS Structure

```css
#resume-preview .project-header {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  margin-top: 5px;
  margin-bottom: 3px;
}

#resume-preview .project-date {
  flex-shrink: 0;
  margin-left: 20px;
  font-style: italic;
  white-space: nowrap;
}
```

## 🔧 Configuration

### Environment Variables

```bash
# Required for API authentication
API_KEY=your-secret-api-key

# Optional: Port (default: 3000)
PORT=3000

# Optional: Node environment
NODE_ENV=production
```

### Default Settings

Default settings are now centralized in `src/config/defaults.js`:

```javascript
{
  fontSize: "12px",
  lineHeight: 1,
  marginTop: "20px",
  marginBottom: "20px",
  marginLeft: "25px",
  marginRight: "25px"
}
```

## 🚀 Deployment

### Local Development

```bash
# Start development server with auto-reload
npm run dev

# Start production server
npm start
```

### Vercel Deployment

The project is configured for Vercel serverless deployment:

```bash
# Deploy to Vercel
vercel

# Set environment variables
vercel env add API_KEY
```

### Docker (Optional)

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

## 📁 Project Structure

```
CV-Factory-nodejs/
├── src/
│   ├── middleware/
│   │   └── auth.js           # API authentication
│   ├── templates/
│   │   └── base.html         # HTML template
│   ├── cli.js                # Command-line interface
│   ├── generator.js          # Core PDF generation logic
│   └── index.js              # Express server
├── examples/
│   ├── cv.md                 # Example markdown CV
│   └── styles.css            # Example CSS styles
├── package.json
├── vercel.json               # Vercel deployment config
└── README.md
```

## 🛠️ Development

### Adding New Features

1. **Core Logic**: Modify `src/generator.js`
2. **CLI Options**: Update `src/cli.js`
3. **API Endpoints**: Edit `src/index.js`
4. **Styling**: Enhance `examples/styles.css`

### Testing

```bash
# Test CLI generation
node src/cli.js -i examples/cv.md -c examples/styles.css -o test.pdf

# Test API (with server running)
curl -H "X-API-Key: test-key" \
  -F "markdown=@examples/cv.md" \
  -F "css=@examples/styles.css" \
  http://localhost:3000/generate \
  --output api-test.pdf
```

## 🐛 Troubleshooting

### Common Issues

**Chrome/Puppeteer Issues:**

```bash
# Install Chrome for testing
npx puppeteer browsers install chrome

# On Linux, install dependencies
sudo apt-get install -y gconf-service libasound2 libatk1.0-0 libc6 libcairo2
```

**API Authentication:**

```bash
# Ensure API_KEY is set
echo $API_KEY

# Check headers in request
curl -v -H "X-API-Key: your-key" http://localhost:3000/health
```

**PDF Generation:**

- Ensure markdown has proper frontmatter
- Check CSS file syntax
- Verify file paths are correct
- Check server logs for detailed errors

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📧 Support

For issues and questions:

- Create an issue on GitHub
- Check the troubleshooting section
- Review the example files
