import yargs from "yargs";
import { hideBin } from "yargs/helpers";
import <PERSON>VGenerator from "./generator.js";
import path from "path";
import { DEFAULT_OPTIONS } from "./config/defaults.js";

const argv = yargs(hideBin(process.argv))
  .usage("Usage: $0 -i <input.md> -c <styles.css> -o <output.pdf>")
  .option("input", {
    alias: "i",
    describe: "Input markdown file",
    type: "string",
    demandOption: true,
  })
  .option("css", {
    alias: "c",
    describe: "CSS file path",
    type: "string",
    demandOption: true,
  })
  .option("output", {
    alias: "o",
    describe: "Output PDF file",
    type: "string",
    default: "cv.pdf",
  })
  .option("fontSize", {
    describe: "Font size",
    type: "string",
    default: DEFAULT_OPTIONS.fontSize,
  })
  .option("lineHeight", {
    describe: "Line height",
    type: "number",
    default: DEFAULT_OPTIONS.lineHeight,
  })
  .option("marginTop", {
    describe: "Top margin",
    type: "string",
    default: DEFAULT_OPTIONS.marginTop,
  })
  .option("marginBottom", {
    describe: "Bottom margin",
    type: "string",
    default: DEFAULT_OPTIONS.marginBottom,
  })
  .option("marginLeft", {
    describe: "Left margin",
    type: "string",
    default: DEFAULT_OPTIONS.marginLeft,
  })
  .option("marginRight", {
    describe: "Right margin",
    type: "string",
    default: DEFAULT_OPTIONS.marginRight,
  })
  .help().argv;

async function main() {
  const generator = new CVGenerator();

  try {
    console.log("Generating CV PDF...");

    const options = {
      fontSize: argv.fontSize,
      lineHeight: argv.lineHeight,
      marginTop: argv.marginTop,
      marginBottom: argv.marginBottom,
      marginLeft: argv.marginLeft,
      marginRight: argv.marginRight,
    };

    await generator.generatePDF(
      path.resolve(argv.input),
      path.resolve(argv.css),
      path.resolve(argv.output),
      options
    );

    console.log(`✓ PDF generated successfully: ${argv.output}`);
  } catch (error) {
    console.error("✗ Error:", error.message);
    process.exit(1);
  }
}

main();
